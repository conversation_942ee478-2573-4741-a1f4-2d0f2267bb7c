'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-10-29 14:54:23
FilePath: /global_rtc_client/utils/perf/perf_upload.py
Description: 性能数据上传工具
'''
from typing import Optional
import os
from apis.perf_api import perf_api
from utils.common.tos_utils import TosClient
from utils.common.log_utils import get_logger
import asyncio

logger = get_logger(__name__)


class PerfUpload:
    """性能数据上传器"""

    def __init__(self):
        """初始化性能数据上传器"""
        self.tos_client = TosClient()

    async def _upload_file_to_tos(self, obj_key: str, content: bytes) -> bool:
        """上传文件到TOS
        
        Args:
            obj_key: 对象键
            content: 文件数据
            
        Returns:
            bool: 是否上传成功
        """
        logger.debug(f"准备上传文件到TOS，对象键: {obj_key}，文件大小: {len(content)} bytes")
        try:
            # 上传文件到TOS
            await asyncio.to_thread(self.tos_client.content_upload, obj_key, content)
            return True
        
        except Exception as e:
            logger.error(f"上传文件到TOS失败: {str(e)}")
            return False

    def _build_perf_path(self, file_path: str, filename: str = None) -> str:
        """构建性能模块的TOS路径

        Args:
            file_path: 完整的文件路径
            filename: 目标文件名

        Returns:
            str: 性能模块的TOS路径
        """
        # 获取文件所在目录
        dir_path = os.path.dirname(file_path)
        if filename:
            if ".tasks/" in dir_path:
                tasks_index = dir_path.find(".tasks/")
                relative_path = dir_path[tasks_index + 7:]
                path = os.path.join('performance_data', relative_path, filename)
            else:
                path = os.path.join(dir_path, filename)
        else:
            if ".tasks/" in file_path:
                tasks_index = file_path.find(".tasks/")
                relative_path = file_path[tasks_index + 7:]
                path = os.path.join('performance_data', relative_path)
            else:
                path = file_path
        logger.debug(f"构建TOS存储路径: {path}")
        return path

    async def upload_perf_file_to_tos(self, file_path: Optional[str], target_filename: str = None) -> Optional[str]:
        """通用的性能文件上传方法

        Args:
            file_path: 要上传的文件路径
            target_filename: 目标文件名，如果为None则使用原文件名

        Returns:
            Optional[str]: 上传成功返回TOS路径，失败返回None
        """
        logger.debug(f"准备上传性能文件: {file_path}")
        try:
            if file_path is None:
                logger.warning("文件路径为None，跳过上传")
                return None

            if not os.path.exists(file_path):
                logger.warning(f"性能文件不存在: {file_path}")
                return None

            with open(file_path, 'rb') as f:
                file_tos_path = self._build_perf_path(file_path, target_filename)
                if await self._upload_file_to_tos(file_tos_path, f.read()):
                    logger.debug(f"上传性能文件成功: {file_tos_path}")
                    return file_tos_path
            return None
        except Exception as e:
            logger.error(f"上传性能文件失败: {str(e)}")
            return None

    async def upload_all_screenshots_to_tos(self, run_dir: str) -> list[str]:
        """上传指定时间戳文件夹下的所有截图文件到TOS
        
        Args:
            sub_task_id: 子任务ID
            timestamp: 时间戳
            run_dir: 运行目录路径
            
        Returns:
            list[str]: 所有成功上传的截图文件TOS路径列表
        """
        logger.debug(f"准备上传所有截图文件，目录: {run_dir}")
        try:
            if not os.path.exists(run_dir):
                logger.warning(f"运行目录不存在: {run_dir}")
                return []

            uploaded_urls = []
            for filename in os.listdir(run_dir):
                if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    screenshot_path = os.path.join(run_dir, filename)
                    with open(screenshot_path, 'rb') as f:
                        screenshot_url = self._build_perf_path(screenshot_path, filename)
                        if await self._upload_file_to_tos(screenshot_url, f.read()):
                            uploaded_urls.append(screenshot_url)
                        else:
                            logger.warning(f"上传截图文件失败: {screenshot_path}")
            logger.debug(f"上传所有截图文件成功: {uploaded_urls}")
            return uploaded_urls
            
        except Exception as e:
            logger.error(f"上传所有截图文件失败: {str(e)}")
            return []

    async def upload_multiple_files_to_tos(self, file_paths: list[str], file_type: str = "perf_data") -> Optional[dict]:
        """上传多个文件到TOS并返回JSON格式的URL映射

        Args:
            file_paths: 文件路径列表
            file_type: 文件类型，用于日志记录

        Returns:
            Optional[dict]: 成功返回 {"filename": "tos_path", ...}，失败返回None
        """
        logger.debug(f"准备上传多个{file_type}文件: {file_paths}")

        if not file_paths:
            logger.debug(f"没有{file_type}文件需要上传")
            return None

        url_mapping = {}

        for file_path in file_paths:
            if not file_path or not os.path.exists(file_path):
                logger.warning(f"{file_type}文件不存在: {file_path}")
                continue

            try:
                filename = os.path.basename(file_path)
                with open(file_path, 'rb') as f:
                    tos_path = self._build_perf_path(file_path, filename)
                    if await self._upload_file_to_tos(tos_path, f.read()):
                        url_mapping[filename] = tos_path
                        logger.debug(f"上传{file_type}文件成功: {filename} -> {tos_path}")
                    else:
                        logger.warning(f"上传{file_type}文件失败: {filename}")

            except Exception as e:
                logger.error(f"上传{file_type}文件异常: {file_path}, 错误: {e}")

        if url_mapping:
            logger.info(f"成功上传 {len(url_mapping)} 个{file_type}文件")
            return url_mapping
        else:
            logger.warning(f"没有{file_type}文件上传成功")
            return None

    async def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'perf_api'):
                await perf_api.close()
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

perf_upload = PerfUpload()

if __name__ == "__main__":
    asyncio.run(perf_upload._build_perf_path("/Users/<USER>/CodeHub/global-business/global_rtc_client/.tasks/283/393/results/基准包/视频直播/1752229389/case.log"))